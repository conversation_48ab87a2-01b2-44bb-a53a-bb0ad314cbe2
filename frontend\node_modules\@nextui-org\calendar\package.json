{"name": "@nextui-org/calendar", "version": "2.2.9", "description": "A calendar displays one or more date grids and allows users to select a single date.", "keywords": ["calendar"], "author": "<PERSON> <<EMAIL>>", "homepage": "https://nextui.org", "license": "MIT", "main": "dist/index.js", "sideEffects": false, "files": ["dist"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/nextui-org/nextui.git", "directory": "packages/components/calendar"}, "bugs": {"url": "https://github.com/nextui-org/nextui/issues"}, "peerDependencies": {"@nextui-org/system": ">=2.4.0", "@nextui-org/theme": ">=2.4.0", "framer-motion": ">=11.5.6 || >=12.0.0-alpha.1", "react": ">=18 || >=19.0.0-rc.0", "react-dom": ">=18 || >=19.0.0-rc.0"}, "dependencies": {"@internationalized/date": "3.6.0", "@react-aria/calendar": "3.6.0", "@react-aria/focus": "3.19.0", "@react-aria/i18n": "3.12.4", "@react-stately/calendar": "3.6.0", "@react-types/button": "3.10.1", "@react-aria/visually-hidden": "3.8.18", "@react-aria/utils": "3.26.0", "@react-stately/utils": "3.10.5", "@react-types/calendar": "3.5.0", "@react-aria/interactions": "3.22.5", "@react-types/shared": "3.26.0", "scroll-into-view-if-needed": "3.0.10", "@types/lodash.debounce": "^4.0.7", "@nextui-org/react-utils": "2.1.3", "@nextui-org/shared-utils": "2.1.2", "@nextui-org/shared-icons": "2.1.1", "@nextui-org/framer-utils": "2.1.6", "@nextui-org/use-aria-button": "2.2.4", "@nextui-org/button": "2.2.9", "@nextui-org/dom-animation": "2.1.1"}, "clean-package": "../../../clean-package.config.json", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}, "./package.json": "./package.json"}, "scripts": {"build": "tsup src --dts", "build:fast": "tsup src", "dev": "pnpm build:fast --watch", "clean": "rimraf dist .turbo", "typecheck": "tsc --noEmit"}}