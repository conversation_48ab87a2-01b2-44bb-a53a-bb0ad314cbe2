import * as react_jsx_runtime from 'react/jsx-runtime';

interface DropdownTriggerProps {
    children?: React.ReactNode;
    className?: string;
    [key: string]: any;
}
/**
 * DropdownTrigger opens the popover's content. It must be an interactive element
 * such as `button` or `a`.
 */
declare const DropdownTrigger: {
    (props: DropdownTriggerProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};

export { DropdownTriggerProps, DropdownTrigger as default };
