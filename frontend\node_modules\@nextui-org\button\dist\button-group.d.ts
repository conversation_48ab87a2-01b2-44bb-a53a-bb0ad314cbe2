import * as _nextui_org_system from '@nextui-org/system';
import { UseButtonGroupProps } from './use-button-group.js';
import 'react';
import './button.js';
import './use-button.js';
import '@nextui-org/theme';
import '@nextui-org/use-aria-button';
import '@nextui-org/ripple';
import '@nextui-org/react-utils';

interface ButtonGroupProps extends UseButtonGroupProps {
}
declare const ButtonGroup: _nextui_org_system.InternalForwardRefRenderFunction<"div", ButtonGroupProps, never>;

export { ButtonGroupProps, ButtonGroup as default };
