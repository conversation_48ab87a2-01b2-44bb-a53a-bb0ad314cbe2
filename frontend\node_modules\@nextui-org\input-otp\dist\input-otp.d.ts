import * as _nextui_org_system from '@nextui-org/system';
import { UseInputOtpProps } from './use-input-otp.js';
import 'input-otp';
import 'tailwind-variants';
import 'react';
import '@nextui-org/theme';
import '@nextui-org/react-utils';
import '@react-types/textfield';

interface InputOtpProps extends UseInputOtpProps {
}
declare const InputOtp: _nextui_org_system.InternalForwardRefRenderFunction<"input", InputOtpProps, never>;

export { InputOtpProps, InputOtp as default };
