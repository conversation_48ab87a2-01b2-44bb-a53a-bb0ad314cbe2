"use client";
import {
  useNavbarContext
} from "./chunk-CLWTNWD4.mjs";

// src/navbar-menu-toggle.tsx
import { useToggleButton as useAriaToggleButton } from "@react-aria/button";
import { forwardRef } from "@nextui-org/system";
import { useDOMRef } from "@nextui-org/react-utils";
import { clsx, dataAttr } from "@nextui-org/shared-utils";
import { useToggleState } from "@react-stately/toggle";
import { useFocusRing } from "@react-aria/focus";
import { mergeProps } from "@react-aria/utils";
import { useHover } from "@react-aria/interactions";
import { useMemo } from "react";
import { jsx, jsxs } from "react/jsx-runtime";
var NavbarMenuToggle = forwardRef((props, ref) => {
  var _a;
  const {
    as,
    icon,
    className,
    onChange,
    autoFocus,
    srOnlyText: srOnlyTextProp,
    ...otherProps
  } = props;
  const Component = as || "button";
  const domRef = useDOMRef(ref);
  const { slots, classNames, isMenuOpen, setIsMenuOpen } = useNavbarContext();
  const handleChange = (isOpen) => {
    onChange == null ? void 0 : onChange(isOpen);
    setIsMenuOpen(isOpen);
  };
  const state = useToggleState({ ...otherProps, isSelected: isMenuOpen, onChange: handleChange });
  const { buttonProps, isPressed } = useAriaToggleButton(props, state, domRef);
  const { isFocusVisible, focusProps } = useFocusRing({ autoFocus });
  const { isHovered, hoverProps } = useHover({});
  const toggleStyles = clsx(classNames == null ? void 0 : classNames.toggle, className);
  const child = useMemo(() => {
    if (typeof icon === "function") {
      return icon(isMenuOpen != null ? isMenuOpen : false);
    }
    return icon || /* @__PURE__ */ jsx("span", { className: slots.toggleIcon({ class: classNames == null ? void 0 : classNames.toggleIcon }) });
  }, [icon, isMenuOpen, slots.toggleIcon, classNames == null ? void 0 : classNames.toggleIcon]);
  const srOnlyText = useMemo(() => {
    if (srOnlyTextProp) {
      return srOnlyTextProp;
    }
    return state.isSelected ? "close navigation menu" : "open navigation menu";
  }, [srOnlyTextProp, isMenuOpen]);
  return /* @__PURE__ */ jsxs(
    Component,
    {
      ref: domRef,
      className: (_a = slots.toggle) == null ? void 0 : _a.call(slots, { class: toggleStyles }),
      "data-focus-visible": dataAttr(isFocusVisible),
      "data-hover": dataAttr(isHovered),
      "data-open": dataAttr(isMenuOpen),
      "data-pressed": dataAttr(isPressed),
      ...mergeProps(buttonProps, focusProps, hoverProps, otherProps),
      children: [
        /* @__PURE__ */ jsx("span", { className: slots.srOnly(), children: srOnlyText }),
        child
      ]
    }
  );
});
NavbarMenuToggle.displayName = "NextUI.NavbarMenuToggle";
var navbar_menu_toggle_default = NavbarMenuToggle;

export {
  navbar_menu_toggle_default
};
