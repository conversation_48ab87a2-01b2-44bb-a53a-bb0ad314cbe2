import * as _nextui_org_react_utils from '@nextui-org/react-utils';
import * as input_otp from 'input-otp';
import * as _nextui_org_theme from '@nextui-org/theme';
import * as tailwind_variants from 'tailwind-variants';
import * as react from 'react';
import * as _nextui_org_system from '@nextui-org/system';

declare const InputOtpProvider: react.Provider<{
    Component: _nextui_org_system.As<any>;
    inputRef: react.RefObject<HTMLInputElement>;
    length: number;
    value: string;
    type: string | undefined;
    slots: {
        base: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        wrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        input: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segmentWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segment: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        passwordChar: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        caret: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        helperWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        errorMessage: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        description: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
    } & {
        base: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        wrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        input: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segmentWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segment: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        passwordChar: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        caret: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        helperWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        errorMessage: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        description: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
    } & {};
    hasHelper: boolean;
    classNames: _nextui_org_theme.SlotsToClasses<"base" | "input" | "wrapper" | "segmentWrapper" | "segment" | "passwordChar" | "caret" | "helperWrapper" | "errorMessage" | "description"> | undefined;
    isInvalid: boolean;
    description: react.ReactNode;
    errorMessage: react.ReactNode;
    isFocusVisible: boolean;
    isFocused: boolean;
    getBaseProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getInputOtpProps: (props?: Partial<input_otp.OTPInputProps>) => Omit<input_otp.OTPInputProps, "children" | "render"> & {
        ref?: _nextui_org_react_utils.ReactRef<HTMLInputElement> | undefined;
    };
    getSegmentWrapperProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getHelperWrapperProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getErrorMessageProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getDescriptionProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
}>;
declare const useInputOtpContext: () => {
    Component: _nextui_org_system.As<any>;
    inputRef: react.RefObject<HTMLInputElement>;
    length: number;
    value: string;
    type: string | undefined;
    slots: {
        base: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        wrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        input: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segmentWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segment: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        passwordChar: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        caret: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        helperWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        errorMessage: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        description: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
    } & {
        base: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        wrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        input: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segmentWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segment: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        passwordChar: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        caret: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        helperWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        errorMessage: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        description: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
    } & {};
    hasHelper: boolean;
    classNames: _nextui_org_theme.SlotsToClasses<"base" | "input" | "wrapper" | "segmentWrapper" | "segment" | "passwordChar" | "caret" | "helperWrapper" | "errorMessage" | "description"> | undefined;
    isInvalid: boolean;
    description: react.ReactNode;
    errorMessage: react.ReactNode;
    isFocusVisible: boolean;
    isFocused: boolean;
    getBaseProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getInputOtpProps: (props?: Partial<input_otp.OTPInputProps>) => Omit<input_otp.OTPInputProps, "children" | "render"> & {
        ref?: _nextui_org_react_utils.ReactRef<HTMLInputElement> | undefined;
    };
    getSegmentWrapperProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getHelperWrapperProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getErrorMessageProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getDescriptionProps: _nextui_org_system.PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
};

export { InputOtpProvider, useInputOtpContext };
