import * as _nextui_org_system from '@nextui-org/system';
import { UseButtonProps } from './use-button.js';
import 'react';
import '@nextui-org/theme';
import '@nextui-org/use-aria-button';
import '@nextui-org/ripple';
import '@nextui-org/react-utils';

interface ButtonProps extends UseButtonProps {
}
declare const Button: _nextui_org_system.InternalForwardRefRenderFunction<"button", ButtonProps, never>;

export { ButtonProps, Button as default };
