"use client";
import {
  useCheckboxGroup
} from "./chunk-5OP3BFLP.mjs";
import {
  CheckboxGroupProvider
} from "./chunk-ACAJT7GC.mjs";

// src/checkbox-group.tsx
import { forwardRef } from "@nextui-org/system";
import { jsx, jsxs } from "react/jsx-runtime";
var CheckboxGroup = forwardRef((props, ref) => {
  const {
    children,
    context,
    label,
    description,
    isInvalid,
    errorMessage,
    getGroupProps,
    getLabelProps,
    getWrapperProps,
    getDescriptionProps,
    getErrorMessageProps
  } = useCheckboxGroup({ ...props, ref });
  return /* @__PURE__ */ jsxs("div", { ...getGroupProps(), children: [
    label && /* @__PURE__ */ jsx("span", { ...getLabelProps(), children: label }),
    /* @__PURE__ */ jsx("div", { ...getWrapperProps(), children: /* @__PURE__ */ jsx(CheckboxGroupProvider, { value: context, children }) }),
    isInvalid && errorMessage ? /* @__PURE__ */ jsx("div", { ...getErrorMessageProps(), children: errorMessage }) : description ? /* @__PURE__ */ jsx("div", { ...getDescriptionProps(), children: description }) : null
  ] });
});
CheckboxGroup.displayName = "NextUI.CheckboxGroup";
var checkbox_group_default = CheckboxGroup;

export {
  checkbox_group_default
};
