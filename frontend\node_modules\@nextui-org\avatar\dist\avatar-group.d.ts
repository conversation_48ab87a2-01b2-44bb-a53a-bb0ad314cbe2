import * as _nextui_org_system from '@nextui-org/system';
import { UseAvatarGroupProps } from './use-avatar-group.js';
import 'react';
import '@nextui-org/theme';
import '@nextui-org/react-utils';
import './avatar.js';
import './use-avatar.js';
import 'tailwind-variants';

interface AvatarGroupProps extends UseAvatarGroupProps {
}
declare const AvatarGroup: _nextui_org_system.InternalForwardRefRenderFunction<"div", AvatarGroupProps, never>;

export { AvatarGroupProps, AvatarGroup as default };
