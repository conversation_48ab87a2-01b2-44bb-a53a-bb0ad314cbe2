import { Direction, DisabledBehavior, Key, KeyboardDelegate, LayoutDelegate, Node, Rect, RefObject, Size, Collection, AriaLabelingProps, DOMAttributes, DOMProps, FocusableElement } from "@react-types/shared";
import { GridCollection, GridNode } from "@react-types/grid";
import { SelectionManager, MultipleSelectionManager } from "@react-stately/selection";
import { GridState } from "@react-stately/grid";
import { SelectableItemStates } from "@react-aria/selection";
import { AriaCheckboxProps } from "@react-types/checkbox";
export interface GridKeyboardDelegateOptions<C> {
    collection: C;
    disabledKeys: Set<Key>;
    disabledBehavior?: DisabledBehavior;
    ref?: RefObject<HTMLElement | null>;
    direction: Direction;
    collator?: Intl.Collator;
    layoutDelegate?: LayoutDelegate;
    /** @deprecated - Use layoutDelegate instead. */
    layout?: DeprecatedLayout;
    focusMode?: 'row' | 'cell';
}
export class GridKeyboardDelegate<T, C extends GridCollection<T>> implements KeyboardDelegate {
    collection: C;
    protected disabledKeys: Set<Key>;
    protected disabledBehavior: DisabledBehavior;
    protected direction: Direction;
    protected collator: Intl.Collator | undefined;
    protected layoutDelegate: LayoutDelegate;
    protected focusMode: any;
    constructor(options: GridKeyboardDelegateOptions<C>);
    protected isCell(node: Node<T>): boolean;
    protected isRow(node: Node<T>): boolean;
    protected findPreviousKey(fromKey?: Key, pred?: (item: Node<T>) => boolean): Key | null;
    protected findNextKey(fromKey?: Key, pred?: (item: Node<T>) => boolean): Key | null;
    protected getKeyForItemInRowByIndex(key: Key, index?: number): Key | null;
    getKeyBelow(fromKey: Key): Key | null;
    getKeyAbove(fromKey: Key): Key | null;
    getKeyRightOf(key: Key): Key | null;
    getKeyLeftOf(key: Key): Key | null;
    getFirstKey(fromKey?: Key, global?: boolean): Key | null;
    getLastKey(fromKey?: Key, global?: boolean): Key | null;
    getKeyPageAbove(fromKey: Key): Key | null;
    getKeyPageBelow(fromKey: Key): Key | null;
    getKeyForSearch(search: string, fromKey?: Key): Key | null;
}
interface DeprecatedLayout {
    getLayoutInfo(key: Key): DeprecatedLayoutInfo;
    getContentSize(): Size;
    virtualizer: DeprecatedVirtualizer;
}
interface DeprecatedLayoutInfo {
    rect: Rect;
}
interface DeprecatedVirtualizer {
    visibleRect: Rect;
}
export interface GridSelectionAnnouncementProps {
    /**
     * A function that returns the text that should be announced by assistive technology when a row is added or removed from selection.
     * @default (key) => state.collection.getItem(key)?.textValue
     */
    getRowText?: (key: Key) => string;
}
interface GridSelectionState<T> {
    /** A collection of items in the grid. */
    collection: Collection<Node<T>>;
    /** A set of items that are disabled. */
    disabledKeys: Set<Key>;
    /** A selection manager to read and update multiple selection state. */
    selectionManager: SelectionManager;
}
export function useGridSelectionAnnouncement<T>(props: GridSelectionAnnouncementProps, state: GridSelectionState<T>): void;
export interface HighlightSelectionDescriptionProps {
    selectionManager: MultipleSelectionManager;
    hasItemActions?: boolean;
}
/**
 * Computes the description for a grid selectable collection.
 * @param props
 */
export function useHighlightSelectionDescription(props: HighlightSelectionDescriptionProps): AriaLabelingProps;
export interface GridProps extends DOMProps, AriaLabelingProps {
    /** Whether the grid uses virtual scrolling. */
    isVirtualized?: boolean;
    /**
     * Whether typeahead navigation is disabled.
     * @default false
     */
    disallowTypeAhead?: boolean;
    /**
     * An optional keyboard delegate implementation for type to select,
     * to override the default.
     */
    keyboardDelegate?: KeyboardDelegate;
    /**
     * Whether initial grid focus should be placed on the grid row or grid cell.
     * @default 'row'
     */
    focusMode?: 'row' | 'cell';
    /**
     * A function that returns the text that should be announced by assistive technology when a row is added or removed from selection.
     * @default (key) => state.collection.getItem(key)?.textValue
     */
    getRowText?: (key: Key) => string;
    /**
     * The ref attached to the scrollable body. Used to provided automatic scrolling on item focus for non-virtualized grids.
     */
    scrollRef?: RefObject<HTMLElement | null>;
    /** Handler that is called when a user performs an action on the row. */
    onRowAction?: (key: Key) => void;
    /** Handler that is called when a user performs an action on the cell. */
    onCellAction?: (key: Key) => void;
    /**
     * Whether pressing the escape key should clear selection in the grid or not.
     *
     * Most experiences should not modify this option as it eliminates a keyboard user's ability to
     * easily clear selection. Only use if the escape key is being handled externally or should not
     * trigger selection clearing contextually.
     * @default 'clearSelection'
     */
    escapeKeyBehavior?: 'clearSelection' | 'none';
}
export interface GridAria {
    /** Props for the grid element. */
    gridProps: DOMAttributes;
}
/**
 * Provides the behavior and accessibility implementation for a grid component.
 * A grid displays data in one or more rows and columns and enables a user to navigate its contents via directional navigation keys.
 * @param props - Props for the grid.
 * @param state - State for the grid, as returned by `useGridState`.
 * @param ref - The ref attached to the grid element.
 */
export function useGrid<T>(props: GridProps, state: GridState<T, GridCollection<T>>, ref: RefObject<HTMLElement | null>): GridAria;
export interface GridRowGroupAria {
    /** Props for the row group element. */
    rowGroupProps: DOMAttributes;
}
/**
 * Provides the accessibility implementation for a row group in a grid.
 */
export function useGridRowGroup(): GridRowGroupAria;
export interface GridRowProps<T> {
    /** An object representing the grid row. Contains all the relevant information that makes up the grid row. */
    node: GridNode<T>;
    /** Whether the grid row is contained in a virtual scroller. */
    isVirtualized?: boolean;
    /** Whether selection should occur on press up instead of press down. */
    shouldSelectOnPressUp?: boolean;
    /**
     * Handler that is called when a user performs an action on the row.
     * Please use onCellAction at the collection level instead.
     * @deprecated
     **/
    onAction?: () => void;
}
export interface GridRowAria extends SelectableItemStates {
    /** Props for the grid row element. */
    rowProps: DOMAttributes;
    /** Whether the row is currently in a pressed state. */
    isPressed: boolean;
}
/**
 * Provides the behavior and accessibility implementation for a row in a grid.
 * @param props - Props for the row.
 * @param state - State of the parent grid, as returned by `useGridState`.
 */
export function useGridRow<T, C extends GridCollection<T>, S extends GridState<T, C>>(props: GridRowProps<T>, state: S, ref: RefObject<FocusableElement | null>): GridRowAria;
export interface GridCellProps {
    /** An object representing the grid cell. Contains all the relevant information that makes up the grid cell. */
    node: GridNode<unknown>;
    /** Whether the grid cell is contained in a virtual scroller. */
    isVirtualized?: boolean;
    /** Whether the cell or its first focusable child element should be focused when the grid cell is focused. */
    focusMode?: 'child' | 'cell';
    /** Whether selection should occur on press up instead of press down. */
    shouldSelectOnPressUp?: boolean;
    /** Indicates how many columns the data cell spans. */
    colSpan?: number;
    /**
     * Handler that is called when a user performs an action on the cell.
     * Please use onCellAction at the collection level instead.
     * @deprecated
     **/
    onAction?: () => void;
}
export interface GridCellAria {
    /** Props for the grid cell element. */
    gridCellProps: DOMAttributes;
    /** Whether the cell is currently in a pressed state. */
    isPressed: boolean;
}
/**
 * Provides the behavior and accessibility implementation for a cell in a grid.
 * @param props - Props for the cell.
 * @param state - State of the parent grid, as returned by `useGridState`.
 */
export function useGridCell<T, C extends GridCollection<T>>(props: GridCellProps, state: GridState<T, C>, ref: RefObject<FocusableElement | null>): GridCellAria;
export interface AriaGridSelectionCheckboxProps {
    /** A unique key for the checkbox. */
    key: Key;
}
export interface GridSelectionCheckboxAria {
    /** Props for the row selection checkbox element. */
    checkboxProps: AriaCheckboxProps;
}
/**
 * Provides the behavior and accessibility implementation for a selection checkbox in a grid.
 * @param props - Props for the selection checkbox.
 * @param state - State of the grid, as returned by `useGridState`.
 */
export function useGridSelectionCheckbox<T, C extends GridCollection<T>>(props: AriaGridSelectionCheckboxProps, state: GridState<T, C>): GridSelectionCheckboxAria;

//# sourceMappingURL=types.d.ts.map
