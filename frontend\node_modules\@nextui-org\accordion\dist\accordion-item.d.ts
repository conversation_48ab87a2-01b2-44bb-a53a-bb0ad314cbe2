import * as _nextui_org_system from '@nextui-org/system';
import { UseAccordionItemProps } from './use-accordion-item.js';
import 'framer-motion';
import './base/accordion-item-base.js';
import '@nextui-org/theme';
import '@nextui-org/aria-utils';
import '@react-types/shared';
import 'react';
import 'tailwind-variants';
import '@nextui-org/react-utils';
import '@react-stately/tree';

interface AccordionItemProps extends UseAccordionItemProps {
}
declare const AccordionItem: _nextui_org_system.InternalForwardRefRenderFunction<"button", AccordionItemProps, never>;

export { AccordionItemProps, AccordionItem as default };
