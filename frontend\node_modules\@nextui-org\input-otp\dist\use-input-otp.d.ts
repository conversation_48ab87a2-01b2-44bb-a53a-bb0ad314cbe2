import * as tailwind_variants from 'tailwind-variants';
import * as react from 'react';
import * as _nextui_org_system from '@nextui-org/system';
import { PropGetter, HTMLNextUIProps } from '@nextui-org/system';
import { InputOtpReturnType, SlotsToClasses, InputOtpSlots, InputOtpVariantProps } from '@nextui-org/theme';
import { ReactRef } from '@nextui-org/react-utils';
import { AriaTextFieldProps } from '@react-types/textfield';
import { OTPInputProps } from 'input-otp';

interface Props extends HTMLNextUIProps<"div"> {
    /**
     * Ref to the DOM node.
     */
    ref?: ReactRef<HTMLInputElement | null>;
    /**
     * Ref to the container DOM node.
     */
    baseRef?: ReactRef<HTMLDivElement | null>;
    /**
     * Length required for the otp.
     */
    length: number;
    /**
     * Regex string for the allowed keys.
     */
    allowedKeys?: string;
    /**
     * Callback that will be fired when the value has length equal to otp length
     */
    onComplete?: (v?: string) => void;
    /**
     * <PERSON><PERSON><PERSON> to disable the input-otp component.
     */
    isDisabled?: boolean;
    /**
     * <PERSON>olean to disable the animation in input-otp component.
     */
    disableAnimation?: boolean;
    /**
     * Classname or List of classes to change the classNames of the element.
     * if `className` is passed, it will be added to the base slot.
     *
     * @example
     * ```ts
     * <InputOtp classNames={{
     *    base:"base-classes",
     *    inputWrapper:"input-wrapper-classes",
     *    input: "input-classes",
     *    segmentWrapper: "segment-wrapper-classes",
     *    segment: "segment-classes",
     *    helperWrapper: "helper-wrapper-classes",
     *    description: "description-classes",
     *    errorMessage: "error-message-classes",
     * }} />
     * ```
     */
    classNames?: SlotsToClasses<InputOtpSlots>;
    /**
     * React aria onChange event.
     */
    onValueChange?: (value: string) => void;
}
type ValueTypes = {
    slots: InputOtpReturnType;
    classNames: SlotsToClasses<InputOtpSlots>;
};
type UseInputOtpProps = Props & InputOtpVariantProps & Omit<AriaTextFieldProps, "onChange"> & Omit<Partial<OTPInputProps>, "render" | "children" | "value" | "onChange" | keyof InputOtpVariantProps>;
declare function useInputOtp(originalProps: UseInputOtpProps): {
    Component: _nextui_org_system.As<any>;
    inputRef: react.RefObject<HTMLInputElement>;
    length: number;
    value: string;
    type: string | undefined;
    slots: {
        base: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        wrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        input: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segmentWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segment: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        passwordChar: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        caret: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        helperWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        errorMessage: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        description: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
    } & {
        base: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        wrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        input: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segmentWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        segment: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        passwordChar: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        caret: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        helperWrapper: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        errorMessage: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
        description: (slotProps?: ({
            color?: "default" | "primary" | "secondary" | "success" | "warning" | "danger" | undefined;
            variant?: "flat" | "faded" | "bordered" | "underlined" | undefined;
            isDisabled?: boolean | undefined;
            isInvalid?: boolean | undefined;
            isReadOnly?: boolean | undefined;
            fullWidth?: boolean | undefined;
            radius?: "none" | "sm" | "md" | "lg" | "full" | undefined;
            size?: "sm" | "md" | "lg" | undefined;
            disableAnimation?: boolean | undefined;
        } & tailwind_variants.ClassProp<ClassValue>) | undefined) => string;
    } & {};
    hasHelper: boolean;
    classNames: SlotsToClasses<"base" | "input" | "wrapper" | "segmentWrapper" | "segment" | "passwordChar" | "caret" | "helperWrapper" | "errorMessage" | "description"> | undefined;
    isInvalid: boolean;
    description: react.ReactNode;
    errorMessage: react.ReactNode;
    isFocusVisible: boolean;
    isFocused: boolean;
    getBaseProps: PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getInputOtpProps: (props?: Partial<OTPInputProps>) => Omit<OTPInputProps, "children" | "render"> & {
        ref?: ReactRef<HTMLInputElement> | undefined;
    };
    getSegmentWrapperProps: PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getHelperWrapperProps: PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getErrorMessageProps: PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
    getDescriptionProps: PropGetter<Record<string, unknown>, _nextui_org_system.DOMAttributes<_nextui_org_system.DOMElement>>;
};
type UseInputOtpReturn = ReturnType<typeof useInputOtp>;

export { UseInputOtpProps, UseInputOtpReturn, ValueTypes, useInputOtp };
