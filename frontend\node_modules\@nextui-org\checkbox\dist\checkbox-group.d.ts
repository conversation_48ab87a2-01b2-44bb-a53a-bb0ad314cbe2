import * as _nextui_org_system from '@nextui-org/system';
import { UseCheckboxGroupProps } from './use-checkbox-group.js';
import 'react';
import '@nextui-org/theme';
import '@react-types/checkbox';
import '@react-types/shared';
import '@nextui-org/react-utils';
import '@react-stately/checkbox';
import './checkbox.js';
import './use-checkbox.js';

interface CheckboxGroupProps extends UseCheckboxGroupProps {
}
declare const CheckboxGroup: _nextui_org_system.InternalForwardRefRenderFunction<"div", CheckboxGroupProps, never>;

export { CheckboxGroupProps, CheckboxGroup as default };
