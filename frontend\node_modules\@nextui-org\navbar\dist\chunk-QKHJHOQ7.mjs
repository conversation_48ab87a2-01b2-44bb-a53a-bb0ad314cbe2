"use client";
import {
  useNavbarContext
} from "./chunk-CLWTNWD4.mjs";
import {
  menuVariants
} from "./chunk-UJDFI5KD.mjs";

// src/navbar-menu.tsx
import { forwardRef } from "@nextui-org/system";
import { useDOMRef } from "@nextui-org/react-utils";
import { clsx, dataAttr } from "@nextui-org/shared-utils";
import { AnimatePresence, LazyMotion, m } from "framer-motion";
import { mergeProps } from "@react-aria/utils";
import { Overlay } from "@react-aria/overlays";
import React from "react";
import { jsx } from "react/jsx-runtime";
var domAnimation = () => import("@nextui-org/dom-animation").then((res) => res.default);
var NavbarMenu = forwardRef((props, ref) => {
  var _a, _b;
  const { className, children, portalContainer, motionProps, style, ...otherProps } = props;
  const domRef = useDOMRef(ref);
  const { slots, isMenuOpen, height, disableAnimation, classNames } = useNavbarContext();
  const styles = clsx(classNames == null ? void 0 : classNames.menu, className);
  const OverlayComponent = isMenuOpen ? Overlay : React.Fragment;
  const contents = disableAnimation ? /* @__PURE__ */ jsx(OverlayComponent, { portalContainer, children: /* @__PURE__ */ jsx(
    "ul",
    {
      ref: domRef,
      className: (_a = slots.menu) == null ? void 0 : _a.call(slots, { class: styles }),
      "data-open": dataAttr(isMenuOpen),
      style: {
        "--navbar-height": typeof height === "number" ? `${height}px` : height
      },
      ...otherProps,
      children
    }
  ) }) : /* @__PURE__ */ jsx(AnimatePresence, { mode: "wait", children: isMenuOpen ? /* @__PURE__ */ jsx(Overlay, { portalContainer, children: /* @__PURE__ */ jsx(LazyMotion, { features: domAnimation, children: /* @__PURE__ */ jsx(
    m.ul,
    {
      ref: domRef,
      layoutScroll: true,
      animate: "enter",
      className: (_b = slots.menu) == null ? void 0 : _b.call(slots, { class: styles }),
      "data-open": dataAttr(isMenuOpen),
      exit: "exit",
      initial: "exit",
      style: {
        "--navbar-height": typeof height === "number" ? `${height}px` : height,
        ...style
      },
      variants: menuVariants,
      ...mergeProps(motionProps, otherProps),
      children
    }
  ) }) }) : null });
  return contents;
});
NavbarMenu.displayName = "NextUI.NavbarMenu";
var navbar_menu_default = NavbarMenu;

export {
  navbar_menu_default
};
