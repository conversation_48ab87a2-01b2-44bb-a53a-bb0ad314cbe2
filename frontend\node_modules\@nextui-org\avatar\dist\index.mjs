"use client";
import {
  avatar_group_default
} from "./chunk-6BQYHIZS.mjs";
import {
  avatar_default
} from "./chunk-4EDCKTQT.mjs";
import {
  AvatarIcon
} from "./chunk-25E6VDTZ.mjs";
import {
  useAvatarGroup
} from "./chunk-YTVNLXJ4.mjs";
import {
  useAvatar
} from "./chunk-GVOGAY7K.mjs";
import {
  AvatarGroupProvider,
  useAvatarGroupContext
} from "./chunk-PM5WBSHT.mjs";
export {
  avatar_default as Avatar,
  avatar_group_default as AvatarGroup,
  AvatarGroupProvider,
  AvatarIcon,
  useAvatar,
  useAvatarGroup,
  useAvatarGroupContext
};
