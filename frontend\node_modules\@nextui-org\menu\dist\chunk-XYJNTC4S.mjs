"use client";
import {
  menu_item_default
} from "./chunk-O2GRGOMW.mjs";

// src/menu-section.tsx
import { menuSection } from "@nextui-org/theme";
import { useMenuSection } from "@react-aria/menu";
import { useMemo } from "react";
import { forwardRef } from "@nextui-org/system";
import { mergeProps } from "@react-aria/utils";
import { clsx } from "@nextui-org/shared-utils";
import { Divider } from "@nextui-org/divider";
import { jsx, jsxs } from "react/jsx-runtime";
var MenuSection = forwardRef(
  ({
    item,
    state,
    as,
    variant,
    color,
    disableAnimation,
    onAction,
    closeOnSelect,
    className,
    classNames,
    showDivider = false,
    hideSelectedIcon,
    dividerProps = {},
    itemClasses,
    title,
    ...otherProps
  }, _) => {
    const Component = as || "li";
    const slots = useMemo(() => menuSection(), []);
    const baseStyles = clsx(classNames == null ? void 0 : classNames.base, className);
    const dividerStyles = clsx(classNames == null ? void 0 : classNames.divider, dividerProps == null ? void 0 : dividerProps.className);
    const { itemProps, headingProps, groupProps } = useMenuSection({
      heading: item.rendered,
      "aria-label": item["aria-label"]
    });
    return /* @__PURE__ */ jsxs(
      Component,
      {
        "data-slot": "base",
        ...mergeProps(itemProps, otherProps),
        className: slots.base({ class: baseStyles }),
        children: [
          item.rendered && /* @__PURE__ */ jsx(
            "span",
            {
              ...headingProps,
              className: slots.heading({ class: classNames == null ? void 0 : classNames.heading }),
              "data-slot": "heading",
              children: item.rendered
            }
          ),
          /* @__PURE__ */ jsxs(
            "ul",
            {
              ...groupProps,
              className: slots.group({ class: classNames == null ? void 0 : classNames.group }),
              "data-has-title": !!item.rendered,
              "data-slot": "group",
              children: [
                [...item.childNodes].map((node) => {
                  const { key: nodeKey, props: nodeProps } = node;
                  let menuItem = /* @__PURE__ */ jsx(
                    menu_item_default,
                    {
                      classNames: itemClasses,
                      closeOnSelect,
                      color,
                      disableAnimation,
                      hideSelectedIcon,
                      item: node,
                      state,
                      variant,
                      onAction,
                      ...nodeProps
                    },
                    nodeKey
                  );
                  if (node.wrapper) {
                    menuItem = node.wrapper(menuItem);
                  }
                  return menuItem;
                }),
                showDivider && /* @__PURE__ */ jsx(
                  Divider,
                  {
                    as: "li",
                    className: slots.divider({
                      class: dividerStyles
                    }),
                    ...dividerProps
                  }
                )
              ]
            }
          )
        ]
      }
    );
  }
);
MenuSection.displayName = "NextUI.MenuSection";
var menu_section_default = MenuSection;

export {
  menu_section_default
};
