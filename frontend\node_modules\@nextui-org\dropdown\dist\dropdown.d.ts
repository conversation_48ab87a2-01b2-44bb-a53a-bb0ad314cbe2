import * as react_jsx_runtime from 'react/jsx-runtime';
import { ReactNode } from 'react';
import { UseDropdownProps } from './use-dropdown.js';
import '@nextui-org/popover';
import '@react-types/menu';
import '@nextui-org/system';
import '@nextui-org/react-utils';
import '@nextui-org/menu';

interface DropdownProps extends UseDropdownProps {
    /**
     * The content of the dropdown. It is usually the `DropdownTrigger`,
     * and `DropdownMenu`
     */
    children: ReactNode[];
}
declare const Dropdown: {
    (props: DropdownProps): react_jsx_runtime.JSX.Element;
    displayName: string;
};

export { DropdownProps, Dropdown as default };
